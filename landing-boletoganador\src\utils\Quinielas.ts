type Loteria = {
  id: number;
  nombre: string;
  numero_total: number;
  cantidad_premios: number;
  isLoto: boolean;
};

export const quinielas: Loteria[] = [
  {
    id: 36,
    nombre: "Anguila 10 AM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 20,
    nombre: "La Primera 12 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 21,
    nombre: "La Suerte 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 33,
    nombre: "King Lottery 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 13,
    nombre: "La Real 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 37,
    nombre: "Anguila 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },

  {
    id: 31,
    nombre: "Florida Dia 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 12,
    nombre: "Gana Mas 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 16,
    nombre: "New York 3:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 15,
    nombre: "Quiniela LoteDom",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 42,
    nombre: "El Quemaito Mayor",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 38,
    nombre: "Anguila 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 29,
    nombre: "La Suerte 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },

  {
    id: 34,
    nombre: "King Lottery 7:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 6,
    nombre: "Loteka 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 18,
    nombre: "La Primera 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 4,
    nombre: "Loteria Nacional 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 5,
    nombre: "Leidsa 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 40,
    nombre: "Anguila 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 32,
    nombre: "Florida 11 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 17,
    nombre: "New York 11:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
];
