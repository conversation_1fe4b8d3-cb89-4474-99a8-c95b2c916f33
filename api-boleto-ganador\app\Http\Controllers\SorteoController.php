<?php

namespace App\Http\Controllers;

use App\Helpers\Utilidades;
use App\Models\Sorteo;
use App\Models\Loteria;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;


class SorteoController extends Controller
{
    public function index($loteriaId)
    {
        $sorteos = Sorteo::where('loteria_id', $loteriaId)->get();

        return response()->json($sorteos);
    }

    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'premios' => 'required|string',
                'numero_sorteo' => 'nullable|string',
                'fecha_sorteo' => 'required|date',
                'loteria_id' => 'required|exists:loterias,id',
            ]);

            $sorteo = Sorteo::create($validatedData);

            return response()->json(['message' => 'Sorteo creado exitosamente', 'sorteo' => $sorteo], 201);
        } catch (ValidationException $e) {
            return response()->json($e->errors(), 422);
        }
    }

    public function show($id)
    {
        $sorteo = Sorteo::findOrFail($id);

        return response()->json($sorteo);
    }

    public function update(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'premios' => 'required|string',
                'numero_sorteo' => 'nullable|string',
                'fecha_sorteo' => 'required|date',
                'loteria_id' => 'required|exists:loterias,id',
            ]);

            $sorteo = Sorteo::findOrFail($id);
            $sorteo->update($validatedData);

            return response()->json(['message' => 'Sorteo actualizado exitosamente', 'sorteo' => $sorteo]);
        } catch (ValidationException $e) {
            return response()->json($e->errors(), 422);
        }
    }

    public function destroy($id)
    {
        $sorteo = Sorteo::findOrFail($id);
        $sorteo->delete();

        return response()->json(['message' => 'Sorteo eliminado exitosamente']);
    }

    public function atrasado()
    {
        $hoy = Carbon::now();
        $fecha = $hoy->toDateString();
        $atrasados = CalculosController::atrasados();
        CalculosController::addPrediccion(1000, $atrasados, 'atrasados', $fecha, 0);
        return response()->json('listo');
    }

    /**
     * Busca combinaciones de números en los sorteos
     * Si se especifica 'tripleta=true', genera todas las posibles combinaciones volteando los dígitos
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function buscarCombinaciones(Request $request)
    {
        $loteriaId = $request->input('loteria_id');
        $voltear = $request->input('voltear');
        $posicion = Utilidades::posicion($request->posicion);
        $esTripleta = $request->input('tripleta') === 'true';

        // Obtener el tipo de jugada directamente del parámetro
        $jugada = (int)$request->input('jugada', 1); // Por defecto es Quiniela (1)

        // Verificar si es una búsqueda de Palé con múltiples números
        $numeros = explode(',', $request->input('numeros'));
        $esPaleMultiple = ($jugada === 2 && count($numeros) > 2);

        // Obtener los números de la solicitud
        $busquedaNumeros = explode(',', $request->input('numeros'));
        $busquedaNumeros = array_filter($busquedaNumeros, function ($valor) {
            return $valor >= 0;
        });

        // Formatear los números para asegurar que tienen 2 dígitos
        for ($i = 0; $i < count($busquedaNumeros); $i++) {
            if ($busquedaNumeros[$i] >= 1 && $busquedaNumeros[$i] <= 9) {
                $busquedaNumeros[$i] = '0' . $busquedaNumeros[$i];
            }

            if ($busquedaNumeros[$i] == 0) {
                $busquedaNumeros[$i] = '00';
            }
        }


        // Comportamiento normal (no es tripleta o no tiene 3 números)
        $busquedaNumerosV = '';
        if ($voltear) {
            $busquedaNumerosV = Utilidades::voltearNumerosLoto($busquedaNumeros);
        }


        if ($loteriaId == '301') {
            $loteria = Loteria::findOrFail('9');
        } else {
            $loteria = Loteria::findOrFail($loteriaId);
        }

        if ($loteriaId == '30') {
            if (count($busquedaNumeros) >= 2) {
                if ($voltear == "true") {
                    $busquedaNumeros = array_merge($busquedaNumeros, $busquedaNumerosV);
                    // $busquedaNumeros = array_unique($busquedaNumeros);
                }
                $resultado = DB::table('sorteos')
                    ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                    ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.quinielaDom', 'loterias.img2')

                    ->where(function ($query) use ($busquedaNumeros) {
                        // Añadir una condición LIKE por cada número
                        foreach ($busquedaNumeros as $numero) {
                            $query->orWhere('premios', 'like', "%$numero%");
                        }
                    })
                    ->where('loterias.quinielaDom', true)
                    ->orderBy('sorteos.fecha_sorteo', 'DESC')
                    ->get()
                    ->filter(function ($sorteo) use ($busquedaNumeros, $voltear, $jugada) {
                        $premios = explode('-', $sorteo->premios);
                        $conteoPremios = array_count_values($premios);

                        $coincidencias = 0;

                        foreach ($busquedaNumeros as $numero) {
                            $volteado = $voltear === "true" ? strrev($numero) : null;

                            $estaOriginal = isset($conteoPremios[$numero]);
                            $estaVolteado = $voltear === "true" ? isset($conteoPremios[$volteado]) : false;

                            // Solo contar una vez si está el original o el volteado
                            if ($estaOriginal || $estaVolteado) {
                                $coincidencias++;
                            }
                        }
                        if ($jugada === 2 && $voltear === 'true') {
                            return $coincidencias > 3;
                        }
                        if ($jugada === 3 && $voltear === 'true') {
                            return $coincidencias > 4;
                        } else {
                            return $coincidencias >= $jugada;
                        }
                    })->values();

                return response()->json([
                    'coincidencias' => implode("-", $busquedaNumeros),
                    'sorteos' => $resultado
                ]);
            }

            $resultado = DB::table('sorteos')
                ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.quinielaDom', 'loterias.img2')

                ->where(function ($query) use ($busquedaNumeros) {

                    foreach ($busquedaNumeros as $numero) {
                        $query->Where('premios', 'LIKE', '%' . $numero . '%');
                    }
                })
                ->where('loterias.quinielaDom', true)
                ->orderBy('sorteos.fecha_sorteo', 'DESC')
                ->get();

            $resultadosV = DB::table('sorteos')
                ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.quinielaDom', 'loterias.img2')
                ->when(count($busquedaNumerosV) === 1, function ($query) use ($busquedaNumerosV) {

                    $query->where('premios', 'LIKE', '%' . $busquedaNumerosV[0] . '%');
                })
                ->where('loterias.quinielaDom', true)
                ->orderBy('sorteos.fecha_sorteo', 'DESC')
                ->get();

            if ($voltear == "true") {

                $busquedaNumeros = array_merge($busquedaNumeros, $busquedaNumerosV);
                $resultado = $resultado->merge($resultadosV);

                $resultado =  $resultado->sortByDesc('fecha_sorteo')->values();
            }

            return response()->json([
                'coincidencias' => implode("-", $busquedaNumeros),
                'sorteos' => $resultado
            ]);
        }
        if (!$loteria->isLoto) {

            if (count($busquedaNumeros) >= 2) {
                if ($voltear === "true") {
                    // Agregar números volteados y eliminar duplicados
                    $busquedaNumeros = array_merge($busquedaNumeros, $busquedaNumerosV);
                }

                // Si es jugada tipo Palé (2) y hay más de 2 números, generar todas las combinaciones posibles de pares
                if ($jugada === 2 && count($busquedaNumeros) > 2) {
                    // Depuración
                    Log::info('Búsqueda de pale múltiple con números: ' . implode(', ', $busquedaNumeros));

                    // Generar todas las combinaciones posibles de pares de números
                    // Si voltear está activado, excluir combinaciones de un número y su versión volteada
                    $combinacionesPales = Utilidades::generarCombinaciones($busquedaNumeros, $voltear === "true");

                    // Depuración
                    Log::info('Combinaciones generadas: ' . count($combinacionesPales));

                    // Inicializar array para almacenar resultados
                    $resultadosCombinados = collect();

                    // Buscar cada combinación de pale en los sorteos
                    foreach ($combinacionesPales as $pale) {
                        // Depuración
                        Log::info('Buscando pale: ' . $pale[0] . '-' . $pale[1]);

                        // Asegurarse de que los números estén correctamente formateados
                        $num1 = str_pad($pale[0], 2, '0', STR_PAD_LEFT);
                        $num2 = str_pad($pale[1], 2, '0', STR_PAD_LEFT);

                        // Buscar sorteos que contengan ambos números del pale
                        $resultadosPale = DB::table('sorteos')
                            ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                            ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.img2')
                            ->where('loteria_id', $loteriaId)
                            ->where(function ($query) use ($num1, $num2, $voltear) {
                                // Buscar la combinación original
                                $query->where(function ($q) use ($num1, $num2) {
                                    $q->where('premios', 'like', "%$num1%")
                                        ->where('premios', 'like', "%$num2%");
                                });

                                // Si voltear está activado, buscar también con los números volteados
                                if ($voltear === "true") {
                                    $num1Volteado = strrev($num1);
                                    $num2Volteado = strrev($num2);

                                    $query->orWhere(function ($q) use ($num1Volteado, $num2) {
                                        $q->where('premios', 'like', "%$num1Volteado%")
                                            ->where('premios', 'like', "%$num2%");
                                    });

                                    $query->orWhere(function ($q) use ($num1, $num2Volteado) {
                                        $q->where('premios', 'like', "%$num1%")
                                            ->where('premios', 'like', "%$num2Volteado%");
                                    });

                                    $query->orWhere(function ($q) use ($num1Volteado, $num2Volteado) {
                                        $q->where('premios', 'like', "%$num1Volteado%")
                                            ->where('premios', 'like', "%$num2Volteado%");
                                    });
                                }
                            })
                            ->orderBy('fecha_sorteo', 'DESC')
                            ->get();

                        // Depuración
                        Log::info('Resultados encontrados para este pale: ' . count($resultadosPale));

                        // Combinar con resultados anteriores
                        $resultadosCombinados = $resultadosCombinados->merge($resultadosPale);
                    }

                    // Eliminar duplicados y ordenar por fecha
                    $resultadosCombinados = $resultadosCombinados->unique('id')->sortByDesc('fecha_sorteo')->values();

                    // Depuración
                    Log::info('Total de resultados combinados: ' . count($resultadosCombinados));

                    return response()->json([
                        'coincidencias' => implode("-", $busquedaNumeros),
                        'sorteos' => $resultadosCombinados
                    ]);
                }

                // Comportamiento normal para 2 números
                $resultados = DB::table('sorteos')
                    ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                    ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.img2')
                    ->where('loteria_id', $loteriaId)
                    ->where(function ($query) use ($busquedaNumeros, $jugada) {
                        // Para jugada tipo Palé (2), requerimos que todos los números estén presentes
                        if ($jugada === 2) {
                            foreach ($busquedaNumeros as $numero) {
                                $query->where('premios', 'like', "%$numero%");
                            }
                        }
                        if ($jugada === 3) {
                            // Para otros tipos de jugada, usamos OR para cualquier coincidencia
                            foreach ($busquedaNumeros as $numero) {
                                $query->orWhere('premios', 'like', "%$numero%");
                            }
                        }
                        if ($jugada === 1) {
                            foreach ($busquedaNumeros as $numero) {
                                $query->orWhere('premios', 'like', "%$numero%");
                            }
                        }
                    })
                    ->orderBy('fecha_sorteo', 'DESC')
                    ->get()
                    ->filter(function ($sorteo) use ($busquedaNumeros, $voltear, $jugada) {
                        $premios = explode('-', $sorteo->premios);
                        $conteoPremios = array_count_values($premios);

                        $coincidencias = 0;

                        foreach ($busquedaNumeros as $numero) {
                            $volteado = $voltear === "true" ? strrev($numero) : null;

                            $estaOriginal = isset($conteoPremios[$numero]);
                            $estaVolteado = $voltear === "true" ? isset($conteoPremios[$volteado]) : false;

                            // Solo contar una vez si está el original o el volteado
                            if ($estaOriginal || $estaVolteado) {
                                $coincidencias++;
                            }
                        }
                        if ($jugada === 3 && $voltear === 'true') {
                            return $coincidencias > 4;
                        } else {
                            return $coincidencias >= $jugada;
                        }
                    })->values();

                return response()->json([
                    'coincidencias' => implode("-", $busquedaNumeros),
                    'sorteos' => $resultados
                ]);
            } else if ($voltear == "true") {



                $resultadosV = DB::table('sorteos')
                    ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                    ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.img2')
                    ->where('loteria_id', $loteriaId)
                    ->when(count($busquedaNumerosV) != 2 && $request->posicion == '0', function ($query) use ($busquedaNumerosV) {
                        foreach ($busquedaNumerosV as $numero) {
                            $query->Where('premios', 'LIKE', '%' . $numero . '%');
                        }
                    })
                    ->when(count($busquedaNumerosV) == 1 && $posicion != '', function ($query) use ($busquedaNumerosV, $posicion) {
                        foreach ($busquedaNumerosV as $numero) {
                            $query->whereRaw("FIND_IN_SET(LPAD($numero, 2, '0'), REPLACE(premios, '-', ',')) $posicion");
                        }
                    })
                    ->when(count($busquedaNumerosV) === 2, function ($query) use ($busquedaNumerosV) {

                        if ($busquedaNumerosV[0]  == +$busquedaNumerosV[1]) {
                            $query->whereRaw("LENGTH(premios) - LENGTH(REPLACE(premios, ?, '')) >= LENGTH(?) * 2", [$busquedaNumerosV[0], $busquedaNumerosV[1]]);
                        } else {
                            $query->where('premios', 'LIKE', '%' . $busquedaNumerosV[0] . '%')
                                ->where('premios', 'LIKE', '%' . $busquedaNumerosV[1] . '%');
                        }
                    })
                    ->orderBy('fecha_sorteo', 'DESC')
                    ->get();
            }


            $resultados = DB::table('sorteos')
                ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.img2')
                ->where('loteria_id', $loteriaId)
                ->when(count($busquedaNumeros) != 2 && $request->posicion == '0', function ($query) use ($busquedaNumeros) {
                    foreach ($busquedaNumeros as $numero) {
                        $query->Where('premios', 'LIKE', '%' . $numero . '%');
                    }
                })
                ->when(count($busquedaNumeros) == 1 && $posicion != '', function ($query) use ($busquedaNumeros, $posicion) {
                    foreach ($busquedaNumeros as $numero) {
                        $query->whereRaw("FIND_IN_SET(LPAD($numero, 2, '0'), REPLACE(premios, '-', ',')) $posicion");
                    }
                })
                ->when(count($busquedaNumeros) === 2, function ($query) use ($busquedaNumeros) {

                    if ($busquedaNumeros[0]  == +$busquedaNumeros[1]) {
                        $query->whereRaw("LENGTH(premios) - LENGTH(REPLACE(premios, ?, '')) >= LENGTH(?) * 2", [$busquedaNumeros[0], $busquedaNumeros[1]]);
                    } else {
                        $query->where('premios', 'LIKE', '%' . $busquedaNumeros[0] . '%')
                            ->where('premios', 'LIKE', '%' . $busquedaNumeros[1] . '%');
                    }
                })
                ->orderBy('fecha_sorteo', 'DESC')
                ->get();

            if ($voltear == "true") {

                $busquedaNumeros = array_merge($busquedaNumeros, $busquedaNumerosV);
                $resultados = $resultados->merge($resultadosV);

                $resultados =  $resultados->sortByDesc('fecha_sorteo')->values();
            }

            return response()->json([
                'coincidencias' => implode("-", $busquedaNumeros),
                'sorteos' => $resultados
            ]);
        } else {

            if ($voltear === "true") {
                // Agregar números volteados y eliminar duplicados
                $busquedaNumeros = array_merge($busquedaNumeros, $busquedaNumerosV);
            }

            $resultado = DB::table('sorteos')
                ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                ->select('sorteos.*', 'loterias.nombre as nombre_loteria', 'loterias.img2')

                ->when($loteriaId != '301', function ($query) use ($loteriaId) {
                    $query->where('loteria_id', $loteriaId);
                })
                ->where(function ($query) use ($busquedaNumeros) {
                    foreach ($busquedaNumeros as $numero) {
                        $query->orWhere('premios', 'LIKE', '%' .  $numero . '%');
                    }
                })
                ->orderBy('fecha_sorteo', 'DESC')
                ->get();

            $coincidencias = [];
            $sorteos = [];

            foreach ($resultado as $sorteo) {
                $numerosSorteo = explode('-', $sorteo->premios);
                $numerosCoincidentes = array_intersect($busquedaNumeros, $numerosSorteo);
                $numerosCoincidentes = array_values($numerosCoincidentes);
                if ($loteriaId == '301') {
                    if (count($numerosCoincidentes) >= 1) {

                        $coincidencias[] = implode("-", $numerosCoincidentes);
                        $sorteos[] = $sorteo;
                    }
                } else {
                    if (count($numerosCoincidentes) >= $loteria->busquedad) {

                        $coincidencias[] = implode("-", $numerosCoincidentes);
                        $sorteos[] = $sorteo;
                    }
                }
            }
            return response()->json([
                'coincidencias' => $coincidencias,
                'sorteos' => $sorteos
            ]);
        }
    }


    public function historial(Request $request)
    {
        if ($request->fecha) {
            $fecha = $request->fecha;
        } else {
            $fecha = Carbon::now()->format('Y-m-d');
        }

        $historial = Sorteo::where([['fecha_sorteo', '<=', $fecha], ['loteria_id', $request->id]])->where('fecha_sorteo', '>', '2010-01-01')->orderBy('fecha_sorteo', 'desc')->take(15)->get();

        $numerosRepetidos = [];

        /* $historial2 = Sorteo::where([
            ['fecha_sorteo', '<', $fecha], ['loteria_id', $request->id]
        ])->where('fecha_sorteo', '>', '2010-01-01')->orderBy('fecha_sorteo', 'desc')->take(10)->get();
*/
        foreach ($historial as $sorteo) {
            $numeros = explode('-', $sorteo->premios);
            foreach ($numeros as $numero) {
                $numero = intval($numero);
                if (isset($numerosRepetidos[$numero])) {
                    $numerosRepetidos[$numero]++;
                } else {
                    $numerosRepetidos[$numero] = 1;
                }
            }
        }

        // Filtrar los números que se repiten más de una vez
        $numerosRepetidos = array_filter($numerosRepetidos, function ($repeticiones) {
            return $repeticiones > 1;
        });


        $numerosRepetidos =  array_keys($numerosRepetidos);



        return response()->json(['historial' => $historial, 'numerosRepetidos' => $numerosRepetidos]);
    }

    public function secuencias(Request $request)
    {
        $numeroBuscado = $request->premios;
        $cantidad = $request->cantidad;
        $nSorteos = 2;
        $apartir = '2000-01-01';
        $fecha = $request->fecha;
        $restaFecha = Carbon::parse($fecha);
        $restaFecha = $restaFecha->subDay();
        $currentDate = Carbon::now();
        $previousDay = $currentDate->subDay();
        $currentDate = $previousDay->toDateString();

        if ($request->nSorteos) {
            $nSorteos = $request->nSorteos;
        }
        if ($request->apartir) {
            $apartir = $request->apartir;
        }


        $loteriaId = $request->loteriaId;
        $posicion = $request->posicion;
        $pos = '';
        switch ($posicion) {
            case 0:
                $pos = '';
                break;

            case 1:
                $pos = '=1';
                break;
            case 2:
                $pos = '=2';
                break;
            case 3:
                $pos = '=3';
                break;
        }

        if ($posicion < 4) {

            // Buscar sorteos que tengan el número en primera
            if ($loteriaId === "30") {
                $sorteosConNumero = Sorteo::where([['fecha_sorteo', '>', $apartir], ['fecha_sorteo', '<',  $restaFecha]])
                    ->whereHas('loteria', function ($query) {
                        $query->where('quinielaDom', true);
                    })
                    ->whereRaw("FIND_IN_SET(LPAD($numeroBuscado, 2, '0'), REPLACE(premios, '-', ','))$pos")
                    ->orderBy('fecha_sorteo', 'asc')
                    ->get();
            } else {
                $sorteosConNumero = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '>',  $apartir], ['fecha_sorteo', '<', $restaFecha]])->whereRaw("FIND_IN_SET(LPAD($numeroBuscado, 2, '0'), REPLACE(premios, '-', ','))$pos")
                    ->orderBy('fecha_sorteo', 'asc')
                    ->get();
            }
            /*$salido = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '=', '2023-08-01']])
            ->orderBy('fecha_sorteo', 'asc')
            ->first();*/
            //$numeroSalido = explode('-', $salido->premios);


            // Crear un arreglo para almacenar las relaciones entre números
            $relaciones = [];
            $acompanado = [];

            foreach ($sorteosConNumero as $sorteo) {
                $numeros = explode('-', $sorteo->premios);
                foreach ($numeros as $numero) {
                    if ($numero != $numeroBuscado) {
                        if (!isset($acompanado[$numeroBuscado][$numero])) {
                            $acompanado[$numeroBuscado][$numero] = 0;
                        }


                        $acompanado[$numeroBuscado][$numero]++;
                    }
                }

                if ($loteriaId === '30') {

                    $siguienteSorteos = Sorteo::where('fecha_sorteo', '>', $sorteo->fecha_sorteo)
                        ->whereHas('loteria', function ($query) {
                            $query->where('quinielaDom', true);
                        })
                        ->where('loteria_id', $sorteo->loteria_id)
                        ->orderBy('fecha_sorteo', 'asc')
                        ->take(2)
                        ->get();
                } else {
                    $siguienteSorteos = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '>', $sorteo->fecha_sorteo]])
                        ->orderBy('fecha_sorteo', 'asc')
                        ->take(2)->get();
                }



                foreach ($siguienteSorteos as  $siguienteSorteo) {
                    if ($siguienteSorteo->premios) {
                        $siguientesNumeros = explode('-', $siguienteSorteo->premios);

                        foreach ($siguientesNumeros as $siguienteNumero) {

                            if ($numeroBuscado != $siguienteNumero /*&& !in_array($siguienteNumero, $numeroSalido)*/) {
                                if (!isset($relaciones[$numeroBuscado][$siguienteNumero])) {
                                    $relaciones[$numeroBuscado][$siguienteNumero] = 0;
                                }


                                $relaciones[$numeroBuscado][$siguienteNumero]++;
                            }
                        }
                    }
                }
            }

            // Ordenar las relaciones por la cantidad de ocurrencias
            arsort($relaciones[$numeroBuscado]);
            arsort($acompanado[$numeroBuscado]);

            $numerosProbablesR = array_keys(Utilidades::voltearNumeros($relaciones[$numeroBuscado]));
            $numerosProbablesA = array_keys(Utilidades::voltearNumeros($acompanado[$numeroBuscado]));

            // Obtener los 5 números con más probabilidad
            $numerosProbablesR = array_slice(array_values($numerosProbablesR), 0, $cantidad);
            $numerosProbablesA = array_slice(array_values($numerosProbablesA), 0, $cantidad);



            $coincidencias = [];
            $miSorteo = [];
            $pr = "";
            if ($request->fecha) {
                $miSorteo = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '=', $request->fecha]])
                    ->orderBy('fecha_sorteo', 'asc')
                    ->first();
                if ($miSorteo) {
                    $pr = $miSorteo->premios;
                    $coincidencias = array_intersect(explode('-',   $miSorteo->premios),  $numerosProbablesR);
                    $coincidencias = array_values($coincidencias);
                }
            }

            return response()->json([
                'secuencias' => $numerosProbablesR,
                'acompanado' =>   $numerosProbablesA,
                'coincidencia' =>   $coincidencias,
                'sorteo' =>   $pr,
                'message' => "Los 5 números más probables basados en el número $numeroBuscado han sido determinados."
            ], 200);
        }
        if ($posicion == 5) {
            $numeroSaliente = $this->getUltimoSorteo($loteriaId, $fecha);
            $numeroBuscado = explode('-', $numeroSaliente->premios);
            $numeroVol = Utilidades::voltearNumerosLoto($numeroBuscado);
            $numeroBuscado = array_merge($numeroVol, $numeroBuscado);

            $pales = Utilidades::generarCombinaciones($numeroBuscado);
            $resultados = [];
            $sorteos = [];
            $relaciones = [];
            $numeros_volteado = [];

            foreach ($pales as $pale) {

                $busquedad = Sorteo::select('premios', 'fecha_sorteo')->where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '>',  $apartir], ['fecha_sorteo', '<', $restaFecha], ['premios', 'REGEXP', "(?=.*\b$pale[0]\b)(?=.*\b$pale[1]\b)"]])
                    ->get();

                $resultados = array_merge($resultados, $busquedad->toArray());
            }

            foreach ($resultados as $resultado) {
                $siguienteSorteos = Sorteo::select('premios')->where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '>', $resultado['fecha_sorteo']]])
                    ->orderBy('fecha_sorteo', 'asc')
                    ->take(2)->get();

                $sorteos = array_merge($sorteos, $siguienteSorteos->toArray());
            }

            foreach ($sorteos as  $siguienteSorteo) {
                if ($siguienteSorteo['premios']) {
                    $siguientesNumeros = explode('-', $siguienteSorteo['premios']);

                    foreach ($siguientesNumeros as $siguienteNumero) {
                        if (!isset($relaciones[$siguienteNumero])) {
                            $relaciones[$siguienteNumero] = 0;
                        }
                        $relaciones[$siguienteNumero]++;
                    }
                }
            }
            asort($relaciones);


            //  $numerosSecuencia = Utilidades::voltearNumeros($relaciones);

            return response()->json([
                'secuencias' => array_slice(array_keys($relaciones), 0, $cantidad),
                'acompanado' =>   [],
                'coincidencia' =>   [],
                'sorteo' =>   [],
                'message' => "Los 5 números más probables basados en el número  han sido determinados."
            ], 200);
        }

        if ($posicion ==  4) {
            $numeroSaliente = $this->getUltimoSorteo($loteriaId, $fecha);
            $numeroBuscado = explode('-', $numeroSaliente->premios);
            $datos =  Utilidades::calcularVariaPosicines($numeroBuscado, $loteriaId,  $apartir, $pos, $cantidad, $numeroSaliente->fecha_sorteo);


            $numerosProbablesR =   $datos;



            $coincidencias = [];
            $miSorteo = [];
            $pr = "";

            $numerosProbablesA = [];
            return response()->json([
                'secuencias' => array_values($numerosProbablesR),
                'acompanado' =>   $numerosProbablesA,
                'coincidencia' =>   $coincidencias,
                'sorteo' =>   $pr,
                'message' => "Los 5 números más probables basados en el número  han sido determinados."
            ], 200);
        }
    }

    public function secuenciasLuis(Request $request)
    {
        // Initialize an array to store results for each number
        $results = [];
        $loteriaName = Loteria::find($request->loteriaId)->nombre;
        // Loop through numbers from 1 to 100
        for ($numeroBuscado = 0; $numeroBuscado <= 100; $numeroBuscado++) {
            // Copy the original code and replace occurrences of $numeroBuscado with the loop variable


            $nSorteos = 1;
            $apartir = '2000-01-01';
            if ($request->nSorteos) {
                $nSorteos = $request->nSorteos;
            }
            if ($request->apartir) {
                $apartir = $request->apartir;
            }

            $loteriaId = $request->loteriaId;
            $posicion = $request->posicion;
            $pos = '';
            switch ($posicion) {
                case 0:
                    $pos = '';
                    break;
                case 1:
                    $pos = '=1';
                    break;
                case 2:
                    $pos = '=2';
                    break;
                case 3:
                    $pos = '=3';
                    break;
            }

            // Buscar sorteos que tengan el número en primera
            if ($loteriaId === "30") {
                $sorteosConNumero = Sorteo::where('fecha_sorteo', '>', $apartir)
                    ->whereHas('loteria', function ($query) {
                        $query->where('quinielaDom', true);
                    })
                    ->whereRaw("FIND_IN_SET(LPAD($numeroBuscado, 2, '0'), REPLACE(premios, '-', ','))$pos")
                    ->orderBy('fecha_sorteo', 'asc')
                    ->get();
            } else {
                $sorteosConNumero = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '>',  $apartir]])->whereRaw("FIND_IN_SET(LPAD($numeroBuscado, 2, '0'), REPLACE(premios, '-', ','))$pos")
                    ->orderBy('fecha_sorteo', 'asc')
                    ->get();
            }

            // Crear un arreglo para almacenar las relaciones entre números
            $relaciones = [];
            $acompanado = [];

            foreach ($sorteosConNumero as $sorteo) {
                $numeros = explode('-', $sorteo->premios);

                foreach ($numeros as $numero) {
                    if ($numero != $numeroBuscado) {
                        if (!isset($acompanado[$numeroBuscado][$numero])) {
                            $acompanado[$numeroBuscado][$numero] = 0;
                        }

                        $acompanado[$numeroBuscado][$numero]++;
                    }
                }

                if ($loteriaId === '30') {
                    $siguienteSorteos = Sorteo::where('fecha_sorteo', '>', $sorteo->fecha_sorteo)
                        ->whereHas('loteria', function ($query) {
                            $query->where('quinielaDom', true);
                        })
                        ->where('loteria_id', $sorteo->loteria_id)
                        ->orderBy('fecha_sorteo', 'asc')
                        ->take(1)
                        ->get();
                } else {
                    $siguienteSorteos = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '<', $sorteo->fecha_sorteo]])
                        ->orderBy('fecha_sorteo', 'asc')
                        ->take($request->dia)->get();
                }

                foreach ($siguienteSorteos as  $siguienteSorteo) {
                    if ($siguienteSorteo->premios) {
                        $siguientesNumeros = explode('-', $siguienteSorteo->premios);

                        foreach ($siguientesNumeros as $siguienteNumero) {
                            if ($numeroBuscado != $siguienteNumero) {
                                if (!isset($relaciones[$numeroBuscado][$siguienteNumero])) {
                                    $relaciones[$numeroBuscado][$siguienteNumero] = 0;
                                }

                                $relaciones[$numeroBuscado][$siguienteNumero]++;
                            }
                        }
                    }
                }
            }

            // Ordenar las relaciones por la cantidad de ocurrencias
            asort($relaciones[$numeroBuscado]);
            arsort($acompanado[$numeroBuscado]);

            // Obtener los 5 números con más probabilidad
            $numerosProbablesR = array_slice(array_keys($relaciones[$numeroBuscado]), 0, 15);
            $numerosProbablesA = array_slice(array_keys($acompanado[$numeroBuscado]), 0, 15);
            $coincidencias = [];
            $miSorteo = [];
            $pr = "";
            if ($request->fecha) {
                $miSorteo = Sorteo::where([['loteria_id', '=',  $loteriaId], ['fecha_sorteo', '=', $request->fecha]])
                    ->orderBy('fecha_sorteo', 'asc')
                    ->first();
                if ($miSorteo) {
                    $pr = $miSorteo->premios;
                    $coincidencias = array_intersect(explode('-',   $miSorteo->premios),  $numerosProbablesR);
                    $coincidencias = array_values($coincidencias);
                }
            }

            // Store the results for this number in the $results array
            $results[$numeroBuscado] = [
                'secuencias' => $numerosProbablesR,
                'acompanado' => $numerosProbablesA,
                'numero' => $numeroBuscado,
                'loteria' => $loteriaName,
                'coincindecias' => $relaciones

            ];
        }

        // Return the results as a JSON response
        return response()->json($results, 200);
    }

    public static function getUltimoSorteo($id, $fecha)
    {
        return Sorteo::where([['loteria_id', '=',  $id], ['fecha_sorteo', '<', $fecha]])->orderBy('id', 'desc')->first();
    }

    public static function getUltimoSorteoActual($id, $fecha)
    {
        return Sorteo::where([['loteria_id', '=',  $id], ['fecha_sorteo', '<=', $fecha]])->orderBy('id', 'desc')->first();
    }



    public static function getUltimoSorteoHoy($id, $fecha)
    {
        return Sorteo::where([['loteria_id', '=',  $id], ['fecha_sorteo', '=', $fecha]])->orderBy('id', 'desc')->first();
    }

    public function listaLLoteria()
    {
        $historial = Sorteo::join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')->where('loterias.isLoto', '=', 0)->select('loterias.nombre', 'sorteos.premios', 'sorteos.fecha_sorteo')->orderBy('fecha_sorteo', 'desc')->get();
        return response()->json($historial);
    }

    public function sorteosDespues(Request $request)
    {

        $loteria = Loteria::where('nombre', $request->nombreLoteria)->get();
        $sorteos = Sorteo::where([['fecha_sorteo', '>', $request->fecha_sorteo], ['loteria_id',  $loteria[0]->id]])->orderBy('fecha_sorteo', 'ASC')->take(3)->get();

        return response()->json($sorteos, 200);
    }

    public function guardarLoteDOM(Request $request, $fecha = null)
    {
        try {
            // Configurar límites de tiempo y memoria para el proceso
            set_time_limit(300); // 5 minutos
            ini_set('memory_limit', '512M');

            // Obtener fecha de inicio (hoy si no se especifica)
            $fechaInicio = $fecha ?? $request->input('fecha', date('Y-m-d'));

            // Validar formato de fecha
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fechaInicio)) {
                return response()->json(['error' => 'Formato de fecha inválido. Use YYYY-MM-DD'], 400);
            }

            Log::info("Iniciando recolección de datos desde: {$fechaInicio}");

            // Preparar datos para Excel
            $excelData = [];
            $excelData[] = [
                'Fecha',
                'Hora Sorteo',
                'Sorteo Número',
                'ID',
                'Juego ID',
                'Juego Nombre',
                'Lotería ID',
                'Lotería Nombre',
                'Resultado'
            ];

            $fechaActual = new \DateTime($fechaInicio);
            $totalRegistros = 0;
            $diasProcesados = 0;
            $diasSinDatos = 0;
            $maxDiasSinDatos = 30; // Parar después de 30 días consecutivos sin datos

            Log::info("Comenzando recolección hacia atrás desde: " . $fechaActual->format('Y-m-d'));

            while ($diasSinDatos < $maxDiasSinDatos) {
                $fechaStr = $fechaActual->format('Y-m-d');
                $url = "https://lotedom.com/api/v1/session?fecha={$fechaStr}&express=false";

                Log::info("Consultando fecha: {$fechaStr}");

                try {
                    // Realizar la petición HTTP a la API
                    $response = Http::timeout(30)->get($url);

                    if ($response->successful()) {
                        $data = $response->json();

                        if (!empty($data) && is_array($data)) {
                            Log::info("Datos encontrados para {$fechaStr}: " . count($data) . " registros");

                            // Agregar los datos al array de Excel
                            foreach ($data as $sorteo) {
                                $excelData[] = [
                                    $sorteo['fecha'] ?? '',
                                    $sorteo['hora_sorteo'] ?? '',
                                    $sorteo['sorteo_numero'] ?? '',
                                    $sorteo['id'] ?? '',
                                    $sorteo['juego_id'] ?? '',
                                    $sorteo['juego_nombre'] ?? '',
                                    $sorteo['loteria_id'] ?? '',
                                    $sorteo['loteria_nombre'] ?? '',
                                    $sorteo['resultado'] ?? ''
                                ];
                                $totalRegistros++;
                            }

                            $diasSinDatos = 0; // Resetear contador de días sin datos
                        } else {
                            Log::info("No hay datos para {$fechaStr}");
                            $diasSinDatos++;
                        }
                    } else {
                        Log::warning("Error HTTP {$response->status()} para fecha {$fechaStr}");
                        $diasSinDatos++;
                    }
                } catch (\Exception $e) {
                    Log::error("Error al consultar fecha {$fechaStr}: " . $e->getMessage());
                    $diasSinDatos++;
                }

                $diasProcesados++;

                // Retroceder un día
                $fechaActual->modify('-1 day');

                // Pausa pequeña para no sobrecargar la API
                usleep(100000); // 0.1 segundos

                // Límite de seguridad: no procesar más de 365 días
                if ($diasProcesados >= 365) {
                    Log::info("Alcanzado límite de 365 días procesados");
                    break;
                }
            }

            Log::info("Recolección completada. Total registros: {$totalRegistros}, Días procesados: {$diasProcesados}");

            if ($totalRegistros === 0) {
                return response()->json(['error' => 'No se encontraron datos en ninguna fecha'], 404);
            }

            // Crear el contenido CSV
            $csvContent = '';
            foreach ($excelData as $row) {
                $csvContent .= implode(',', array_map(function ($field) {
                    // Escapar comillas y envolver en comillas si contiene comas
                    if (strpos($field, ',') !== false || strpos($field, '"') !== false) {
                        return '"' . str_replace('"', '""', $field) . '"';
                    }
                    return $field;
                }, $row)) . "\n";
            }

            // Generar nombre de archivo con rango de fechas
            $fechaFinal = $fechaActual->format('Y-m-d');
            $fileName = "lotedom_sorteos_{$fechaInicio}_a_{$fechaFinal}_total_{$totalRegistros}.csv";

            Log::info("Generando archivo: {$fileName}");

            // Retornar el archivo CSV como descarga
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', "attachment; filename=\"{$fileName}\"")
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
        } catch (\Exception $e) {
            Log::error("Error en guardarLoteDOM: " . $e->getMessage());
            return response()->json(['error' => 'Error interno del servidor: ' . $e->getMessage()], 500);
        }
    }
}
