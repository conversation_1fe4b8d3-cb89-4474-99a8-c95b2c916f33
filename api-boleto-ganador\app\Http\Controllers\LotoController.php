<?php

namespace App\Http\Controllers;

use App\Models\Loteria;
use App\Models\Sorteo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LotoController extends Controller
{
    public function combinacionLoto(Request $request)
    {
        $id =  $request->loteria_id;
        if ($id == 30) {
            $sorteos = Sorteo::select('fecha_sorteo', 'premios')
                ->join('loterias', 'sorteos.loteria_id', '=', 'loterias.id')
                ->where('loterias.quinielaDom', true)
                ->get();
        } else {
            $sorteos = Sorteo::select('fecha_sorteo', 'premios')
                ->where('loteria_id', '=', $id)->get();
        }



        // Contadores para combinaciones con fechas
        $combinationCounts = [
            '3' => [],
            '4' => [],
            '5' => [],
        ];

        foreach ($sorteos as $sorteo) {
            $premios = explode(',', $sorteo->premios); // Separar combinaciones por comas
            $fechaSorteo = $sorteo->fecha_sorteo; // Fecha del sorteo

            foreach ($premios as $premio) {
                $numbers = explode('-', $premio); // Separar números por guiones
                $numbers = array_map('intval', $numbers); // Convertir a enteros

                // Preparar conjuntos de números para procesar
                $numberSets = [$numbers]; // Siempre incluir números originales

                // Si es lotería ID 30, agregar también el conjunto de números volteados
                if ($id == 30) {
                    $reversedNumbers = array_map(function ($num) {
                        return intval(strrev(sprintf('%02d', $num)));
                    }, $numbers);
                    $numberSets[] = $reversedNumbers; // Agregar como conjunto separado

                    // Log para depuración
                    Log::info('Lotería ID 30 - Números originales: ' . implode(',', $numbers));
                    Log::info('Lotería ID 30 - Números volteados: ' . implode(',', $reversedNumbers));
                }

                // Procesar cada conjunto de números por separado
                foreach ($numberSets as $setIndex => $currentNumbers) {
                    if ($id == 30) {
                        Log::info('Procesando conjunto ' . $setIndex . ': ' . implode(',', $currentNumbers));
                    }

                    // Generar y contar combinaciones de 3, 4 y 5
                    foreach ([3, 4, 5] as $size) {
                        if (count($currentNumbers) >= $size) {
                            $combinations = $this->generateCombinations($currentNumbers, $size);
                            foreach ($combinations as $combination) {
                                sort($combination); // Ordenar cada combinación de menor a mayor

                                // Formatear números como "03" si tienen un solo dígito
                                $formattedCombination = array_map(function ($num) {
                                    return sprintf('%02d', $num);
                                }, $combination);

                                $key = implode('-', $formattedCombination); // Crear clave única para la combinación

                                if ($id == 30 && $size == 3) {
                                    Log::info('Generando combinación: ' . $key . ' del conjunto ' . $setIndex);
                                }

                                // Inicializar la combinación si no existe
                                if (!isset($combinationCounts[$size][$key])) {
                                    $combinationCounts[$size][$key] = [
                                        'count' => 0,
                                        'dates' => [],
                                        'combinacion' => implode('-', $formattedCombination), // Guardar como string
                                        'numeros' => $formattedCombination, // Números individuales formateados
                                        'nivel' => $size, // Nivel basado en el tamaño
                                        'key' => $size . '-' . $key, // Generar una clave única con el nivel
                                    ];
                                }

                                // Incrementar el contador y agregar la fecha
                                $combinationCounts[$size][$key]['count']++;
                                $combinationCounts[$size][$key]['dates'][] = $fechaSorteo;
                            }
                        }
                    }
                }
            }
        }

        // Ordenar y transformar las combinaciones en el formato solicitado
        $result = [];
        foreach ($combinationCounts as $size => $combinations) {
            foreach ($combinations as $key => $data) {
                $data['dates'] = array_unique($data['dates']); // Eliminar duplicados de fechas
                rsort($data['dates']); // Ordenar fechas de mayor a menor
                $result[] = $data; // Agregar al resultado final
            }
        }

        // Log para depuración
        if ($id == 30) {
            Log::info('Total de combinaciones generadas para lotería ID 30: ' . count($result));

            // Buscar algunas combinaciones volteadas específicas para verificar
            $volteadasEncontradas = [];
            foreach ($result as $combo) {
                // Buscar combinaciones que podrían ser volteadas (números que terminan en patrones específicos)
                if (
                    strpos($combo['combinacion'], '01-20-32') !== false ||
                    strpos($combo['combinacion'], '08-10-68') !== false ||
                    strpos($combo['combinacion'], '40-58-99') !== false
                ) {
                    $volteadasEncontradas[] = $combo;
                }
            }

            Log::info('Combinaciones volteadas encontradas en resultado: ' . count($volteadasEncontradas));
            if (count($volteadasEncontradas) > 0) {
                Log::info('Ejemplos de combinaciones volteadas: ' . json_encode(array_slice($volteadasEncontradas, 0, 3)));
            }

            Log::info('Primeras 5 combinaciones del resultado: ' . json_encode(array_slice($result, 0, 5)));
        }

        // Ordenar por nivel y luego por count en orden descendente
        usort($result, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });

        return response()->json($result, 200);
    }

    private function generateCombinations($array, $size)
    {
        if ($size > count($array)) {
            return [];
        }

        $results = [];
        $indexes = range(0, $size - 1);

        while (true) {
            $combination = [];
            foreach ($indexes as $i) {
                $combination[] = $array[$i];
            }
            $results[] = $combination;

            for ($i = $size - 1; $i >= 0; $i--) {
                if ($indexes[$i] != $i + count($array) - $size) {
                    break;
                }
            }

            if ($i < 0) {
                break;
            }

            $indexes[$i]++;
            for ($j = $i + 1; $j < $size; $j++) {
                $indexes[$j] = $indexes[$j - 1] + 1;
            }
        }

        return $results;
    }
}
