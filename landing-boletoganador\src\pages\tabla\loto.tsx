import { Suspense, useState, useEffect } from "react";
import { Button, Col, Row, Space, Table, Tag, Select, Input } from "antd";
import { SearchOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import { formatearFecha, formatearNumeros } from "@/utils";
import { loterias } from "@/utils/Loteria";
import Banner from "@/components/Banner";
import useAxios from "@/hook/useAxios";
import RenderIf from "@/components/RenderIf";

interface CombinationData {
  key: string;
  nivel: number;
  combinacion: string;
  count: number;
  dates: string[];
}

const Loto: React.FC = () => {
  const { error, loading, sendRequest } = useAxios();
  const [data, setData] = useState<CombinationData[]>([]);
  const [filteredData, setFilteredData] = useState<CombinationData[]>([]);
  const [loteriaId, setLoteriaId] = useState<number>(9); // Default to ID 9
  const [nivelFilter, setNivelFilter] = useState<number | undefined>(undefined);
  const [searchValue, setSearchValue] = useState<string>("");
  const [expandedRows, setExpandedRows] = useState<string[]>([]);

  useEffect(() => {
    fetchCombinations();
  }, [loteriaId]);

  const formatCombination = (value: string): string => {
    // Elimina todos los caracteres que no sean dígitos
    const sanitized = value.replace(/[^0-9]/g, "");

    // Agrupa en pares de 2 y los une con guiones
    return sanitized.match(/.{1,2}/g)?.join("-") || sanitized;
  };

  const fetchCombinations = async (): Promise<void> => {
    if (!loteriaId) return;
    try {
      const response = await sendRequest({
        url: `loto/combinaciones?loteria_id=${loteriaId}`,
        method: "GET",
      });
      const combinations = response.data as CombinationData[];
      setData(combinations);
      setFilteredData(combinations);
    } catch (err) {
      console.error(error);
    }
  };

  const handleNivelFilter = (value: number | undefined) => {
    setNivelFilter(value);
    filterData(searchValue, value);
  };

  const handleSearch = () => {
    filterData(searchValue, nivelFilter);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCombination(e.target.value);
    setSearchValue(formattedValue);
  };

  const filterData = (search: string, nivel?: number) => {
    let filtered = data;

    if (nivel !== undefined && nivel !== 0) {
      filtered = filtered.filter((item) => item.nivel === nivel);
    }

    if (search.trim() !== "") {
      const searchNumbers = search.split("-");
      filtered = filtered.filter((item) => {
        const combinationNumbers = formatCombination(item.combinacion).split(
          "-"
        );
        return searchNumbers.every((num) => combinationNumbers.includes(num));
      });
    }

    setFilteredData(filtered);
  };

  const toggleRowExpansion = (key: string) => {
    setExpandedRows((prevExpandedRows) =>
      prevExpandedRows.includes(key)
        ? prevExpandedRows.filter((rowKey) => rowKey !== key)
        : [...prevExpandedRows, key]
    );
  };

  const columns: ColumnsType<CombinationData> = [
    {
      title: "Nivel",
      dataIndex: "nivel",
      key: "nivel",
      sorter: (a, b) => a.nivel - b.nivel,
      render: (nivel) => <Tag color="purple">{nivel}</Tag>,
    },
    {
      title: "Combinación",
      dataIndex: "combinacion",
      key: "combinacion",
      render: (text) => (
        <Space wrap size={10}>
          {formatearNumeros(text).map((value: string) => {
            return (
              <RenderIf condition={value != ""}>
                <div
                  className="
              numerosSeleccionadoHoy
          "
                  key={value}
                >
                  {value}
                </div>
              </RenderIf>
            );
          })}
        </Space>
      ),
    },
    {
      title: "Cantidad",
      dataIndex: "count",
      key: "count",
      sorter: (a, b) => a.count - b.count,
      render: (cant) => <Tag color="pink">{cant}</Tag>,
    },
    {
      title: "Fechas",
      dataIndex: "dates",
      key: "dates",
      sorter: (a, b) => {
        const dateA = new Date(a.dates[0]).getTime();
        const dateB = new Date(b.dates[0]).getTime();
        return dateA - dateB;
      },
      render: (dates: string[], record) => {
        const isExpanded = expandedRows.includes(record.key);
        return (
          <div>
            <div>
              <Tag color="green" key={dates[0]}>
                {formatearFecha(dates[0])}
              </Tag>
            </div>
            {dates.length > 1 && (
              <div style={{ marginTop: 8 }}>
                <Button
                  type="link"
                  icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                  onClick={() => toggleRowExpansion(record.key)}
                >
                  {isExpanded ? "Mostrar menos" : "Mostrar más"}
                </Button>
              </div>
            )}
            {isExpanded && (
              <Space direction="vertical" style={{ marginTop: 10 }}>
                {dates.slice(1).map((date) => (
                  <Tag color="green" key={date}>
                    {formatearFecha(date)}
                  </Tag>
                ))}
              </Space>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ minHeight: "80vh", marginBottom: 60 }}>
      <Banner type="secondary" title="Tabla Loto" />
      <div
        className="container"
        style={{ marginTop: -125, position: "relative" }}
      >
        <div className="cajaNumero">
          <div className="header-area">
            <h2 className="titleContent center">Generar la Tabla</h2>
            <Col span={24} className="center">
              <Space direction="vertical" style={{ marginRight: 8 }}>
                <h4 className="title">Selecciona un Loto</h4>
                <Select
                  placeholder="Selecciona una lotería"
                  size="large"
                  style={{ width: 280 }}
                  value={loteriaId}
                  onChange={(value) => setLoteriaId(value)}
                  options={loterias
                    .filter(
                      (loteria) =>
                        (loteria.isLoto || loteria.id === 30) &&
                        loteria.id != 8 &&
                        loteria.id != 23
                    )
                    .map((loteria) => ({
                      label: loteria.nombre,
                      value: loteria.id,
                    }))}
                />
              </Space>
              <Space direction="vertical" style={{ marginLeft: 8 }}>
                <h4 className="title">Cant. de numeros</h4>
                <Select
                  size="large"
                  placeholder="Filtrar por nivel"
                  style={{ width: 300 }}
                  onChange={handleNivelFilter}
                  allowClear
                  options={[
                    { label: "3", value: 3 },
                    { label: "4", value: 4 },
                    { label: "5", value: 5 },
                    { label: "Todos", value: 0 },
                  ]}
                />
              </Space>
            </Col>
            <Col span={24} className="center" style={{ marginTop: 10 }}>
              <Space>
                <Input.Group compact>
                  <Input
                    placeholder="Buscar ej: 01-56-78"
                    allowClear
                    value={searchValue}
                    onChange={handleInputChange}
                    size="large"
                    style={{ width: "calc(100% - 75px)" }}
                  />
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    size="large"
                    style={{ width: "75px", borderRadius: "0px 8px 8px 0px" }}
                  ></Button>
                </Input.Group>
              </Space>
            </Col>
          </div>

          <div className="body-area">
            <Row>
              <Col span={24}>
                <Suspense fallback={<div>Loading...</div>}>
                  <Table
                    className="over"
                    dataSource={filteredData}
                    columns={columns}
                    pagination={{ pageSize: 100 }}
                    loading={loading}
                    rowClassName={(_record, index) =>
                      index % 2 === 0 ? "even-row" : "odd-row"
                    }
                  />
                </Suspense>
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Loto;
