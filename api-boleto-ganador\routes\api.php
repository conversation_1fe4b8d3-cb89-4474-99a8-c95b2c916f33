<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CalculosController;
use App\Http\Controllers\CentenaController;
use App\Http\Controllers\LoteriaController;
use App\Http\Controllers\TablaContoller;
use App\Http\Controllers\SorteoController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\LotoController;
use App\Models\ChatMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


Route::prefix('auth')->group(function () {
    Route::post('registrar', [AuthController::class, 'registrar']);
    Route::post('login', [AuthController::class, 'login']);
});


Route::middleware('jwt.auth')->group(function () {
    Route::get('/logout', [AuthController::class, 'logout']);

    Route::group(['prefix' => 'lotery'], function () {
        Route::get('/{id}', [LoteriaController::class, 'show'])->name('loterias.show');
        Route::post('/', [LoteriaController::class, 'store'])->name('loterias.store');
        Route::put('/{id}', [LoteriaController::class, 'update'])->name('loterias.update');
        Route::delete('/{id}', [LoteriaController::class, 'destroy'])->name('loterias.destroy');
    });

    Route::prefix('sorteos')->group(function () {
        Route::get('/loteria/{loteriaId}', [SorteoController::class, 'index']);

        Route::get('/{id}', [SorteoController::class, 'show']);
        Route::put('/{id}', [SorteoController::class, 'update']);
        Route::delete('/{id}', [SorteoController::class, 'destroy']);
    });

    Route::prefix('tabla')->group(function () {});
});
Route::get('/deco', [CalculosController::class, 'decodeData']);
Route::get('/pales-populares', [CalculosController::class, 'palesPopulares']);
Route::get('/numeros-pronosticados', [CalculosController::class, 'numerosPronosticados']);
Route::get('/numeros-similares', [CalculosController::class, 'numerosSimilares']);
Route::get('/sorteos/a4lfivb2ms/{loteriaId}', [SorteoController::class, 'index']);
Route::get('/atrasado', [SorteoController::class, 'atrasado']);
Route::get('loterias/{id}', [LoteriaController::class, 'show'])->name('loterias.show');
Route::get('inteligencia', [CalculosController::class, 'inteligencia']);
Route::get('millonario', [CalculosController::class, 'millonario']);
Route::get('tiempo-atras', [CalculosController::class, 'tiempoAtras']);
Route::get('calculo/anguila', [CalculosController::class, 'calculoAnguila']);
Route::get('tiempo-atras2', [CalculosController::class, 'tiempoAtras2']);
Route::get('retraso', [CalculosController::class, 'calculo3']);
Route::get('loto/combinaciones', [LotoController::class, 'combinacionLoto']);

Route::get('/sorteos/buscar/combinaciones', [SorteoController::class, 'buscarCombinaciones']);
Route::get('/sorteos/buscar/historial', [SorteoController::class, 'historial']);
Route::get('/lista/loteria', [SorteoController::class, 'listaLLoteria']);
Route::get('/sorteos/getUltimoSorteo/{id}/{fecha}', [SorteoController::class, 'getUltimoSorteo']);
Route::get('/sorteos/getUltimoSorteoActual/{id}', [SorteoController::class, 'getUltimoSorteoActual']);
Route::get('/sorteosDespues', [SorteoController::class, 'sorteosDespues']);
Route::get('/tabla', [TablaContoller::class, 'index']);
Route::get('/tabla/dia/semana', [TablaContoller::class, 'diaSemana']);
Route::get('/loterias', [LoteriaController::class, 'index'])->name('loterias.index');
Route::get('/loto', [LoteriaController::class, 'getLotos']);
Route::get('/getloterias', [LoteriaController::class, 'getLoterias']);
Route::get('/loteria/saber/sorteo', [LoteriaController::class, 'verificarSorteoHoy']);
Route::post('/login', [AuthController::class, 'login']);

Route::get('tabla/create', [TablaContoller::class, 'create']);
Route::get('tabla/pale', [TablaContoller::class, 'tablaPale']);
Route::get('tabla/superpale', [TablaContoller::class, 'tablaSuperPale']);

Route::get('tabla/superpale/resultado', [TablaContoller::class, 'calcularResultado']);
Route::get('tabla/superpale/resultadoPale', [TablaContoller::class, 'calcularResultadoPale']);
Route::get('tabla/superpale-hoy', [TablaContoller::class, 'tablaSuperPaleHoy']);
Route::get('tabla/un-dia-como-hoy', [TablaContoller::class, 'unDiaComoHoy']);
Route::get('tabla/terminales', [TablaContoller::class, 'terminales']);
Route::get('tabla/numeros', [TablaContoller::class, 'numeros']);
Route::get('centena/create', [CentenaController::class, 'create']);
Route::get('quinteto/create', [CentenaController::class, 'quinteto']);
Route::get('quinteto', [CalculosController::class, 'quintetoC']);
Route::get('/companies', [CompanyController::class, 'index'])->name('companies.index');
Route::get('/companies/loterias', [CompanyController::class, 'indexLoterias']);
Route::get('/companies/buscar', [CompanyController::class, 'buscarPorFecha']);
Route::get('/companies/buscar/loterias', [CompanyController::class, 'buscarPorFechaLoterias']);
Route::post('/sorteos', [SorteoController::class, 'store']);

Route::get('/tabla/secuencias', [SorteoController::class, 'secuencias']);
Route::get('/tabla/secuenciasLuis', [SorteoController::class, 'secuenciasLuis']);
Route::get('tabla/numerosPr', [CalculosController::class, 'numerosPr']);
Route::get('loto/chequear', [CalculosController::class, 'chequearLoto']);
Route::get('/predicciones', [LoteriaController::class, 'predicciones']);
Route::get('/el-guebo', [CalculosController::class, 'elGuebo']);
Route::get('/dios/es/mi/guia', [CalculosController::class, 'diosEsMiGuia']);
Route::get('/tabla/caliente', [TablaContoller::class, 'tablaNumeroTop']);
Route::get('/semanas', [CalculosController::class, 'semanas']);

Route::get('/ganador', [CalculosController::class, 'ganador']);
Route::get('/generar-tripletas', [CalculosController::class, 'generarTripletas']);
Route::get('/buscar-tripletas', [CalculosController::class, 'buscarTripletas']);
Route::get('guardar-lote-dom', [SorteoController::class, 'guardarLoteDOM']);
Route::get('guardar-lote-dom/{fecha}', [SorteoController::class, 'guardarLoteDOM']);
Route::get('exportar-lote-dom-historico', [SorteoController::class, 'guardarLoteDOM']);

// Nuevo endpoint para buscar tripletas con todas sus variaciones
Route::get('/buscar-tripleta-completa', [CalculosController::class, 'buscarTripletaCompleta']);

Route::get('/chat-messages',  [CompanyController::class, 'chatMessages']);

Route::post('/store-chat-message', function (Request $request) {
    // Validar los datos recibidos

    $validated = $request->validate([
        'author' => 'required|string|max:255',
        'message' => 'required|string',
        'room' => 'required|string|max:255',
        'created_at' => 'required|date',
        'user_id' => 'required',
    ]);

    // Guardar el mensaje en la base de datos
    $mensaje = ChatMessage::create([
        'author' => $validated['author'],
        'message' => $validated['message'],
        'room' => $validated['room'],
        'created_at' => $validated['created_at'],
        'user_id' => $validated['user_id'],
        'delete' => 0
    ]);

    return response()->json([$mensaje], 201);
});



Route::patch('/chat-messages/{id}/delete', function ($id, Request $request) {
    if ($id != 0) {
        $message = ChatMessage::findOrFail($id);
        $message->delete = true; // Marcar como eliminado
        $message->save();
    }


    return response()->json(['status' => 'Message deleted successfully']);
});
