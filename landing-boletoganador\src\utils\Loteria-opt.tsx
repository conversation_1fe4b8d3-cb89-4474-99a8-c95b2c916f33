type Loteria = {
  id: number;
  nombre: string;
  numero_total: number;
  cantidad_premios: number;
  isLoto: boolean;
  banMovil: number;
  tuJugada: number;
};

export const loterias: Loteria[] = [
  {
    id: 36,
    nombre: "Anguila 10 AM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 1,
  },
  {
    id: 20,
    nombre: "La Primera 12 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 8,
    tuJugada: 2,
  },
  {
    id: 21,
    nombre: "La Suerte 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 56,
    tuJugada: 3,
  },
  {
    id: 33,
    nombre: "King Lottery 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 4,
  },
  {
    id: 13,
    nombre: "La Real 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 4,
    tuJugada: 5,
  },
  {
    id: 37,
    nombre: "Anguila 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 6,
  },

  {
    id: 31,
    nombre: "Florida Dia 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 9,
  },
  {
    id: 12,
    nombre: "Gana Mas 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 2,
    tuJugada: 8,
  },
  {
    id: 16,
    nombre: "New York 3:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 10,
  },
  {
    id: 15,
    nombre: "Quiniela LoteDom ",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 57,
    tuJugada: 7,
  },
  {
    id: 42,
    nombre: "El Quemaito Mayor",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 57,
    tuJugada: 7,
  },
  {
    id: 38,
    nombre: "Anguila 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 12,
  },
  {
    id: 29,
    nombre: "La Suerte 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 172,
    tuJugada: 11,
  },

  {
    id: 34,
    nombre: "King Lottery 7:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 13,
  },
  {
    id: 6,
    nombre: "Loteka 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 5,
    tuJugada: 14,
  },
  {
    id: 18,
    nombre: "La Primera 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 191,
    tuJugada: 15,
  },
  {
    id: 4,
    nombre: "Loteria Nacional 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 1,
    tuJugada: 17,
  },
  {
    id: 5,
    nombre: "Leidsa 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 3,
    tuJugada: 16,
  },
  {
    id: 40,
    nombre: "Anguila 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 0,
    tuJugada: 18,
  },
  {
    id: 32,
    nombre: "Florida 11 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 10,
    tuJugada: 19,
  },
  {
    id: 17,
    nombre: "New York 11:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
    banMovil: 7,
    tuJugada: 20,
  },
  {
    cantidad_premios: 3,
    id: 30,
    isLoto: false,
    nombre: "TODAS LAS QUINIELAS",
    numero_total: 100,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    cantidad_premios: 3,
    id: 1000,
    isLoto: false,
    nombre: "QUINIELAS DOMINICANA",
    numero_total: 100,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    cantidad_premios: 3,
    id: 1001,
    isLoto: false,
    nombre: "QUINIELAS EXTRANJERA",
    numero_total: 100,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 41,
    nombre: "Loto Real 1PM (M/V)",
    numero_total: 38,
    cantidad_premios: 6,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 10,
    nombre: "Loto 5 8PM (Diario)",
    numero_total: 38,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 11,
    nombre: "Mega Lotto 8PM (L/J)",
    numero_total: 38,
    cantidad_premios: 6,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },

  {
    id: 27,
    nombre: "Mega Chance 8PM (Diario)",
    numero_total: 100,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 23,
    nombre: "Pega 3 Mas 9PM (Diario)",
    numero_total: 50,
    cantidad_premios: 3,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },

  {
    id: 9,
    nombre: "Loto Leidsa 9PM (M/S)",
    numero_total: 40,
    cantidad_premios: 6,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },

  {
    id: 7,
    nombre: "Loto Pool 9PM (Diario)",
    numero_total: 31,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },

  {
    id: 8,
    nombre: "Super Kino Tv 9PM (Diario)",
    numero_total: 80,
    cantidad_premios: 20,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 39,
    nombre: "Cash4Life",
    numero_total: 60,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 26,
    nombre: "Mega Millions",
    numero_total: 70,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 24,
    nombre: "Power Ball",
    numero_total: 70,
    cantidad_premios: 5,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
  {
    id: 43,
    nombre: "El Agarra 4",
    numero_total: 100,
    cantidad_premios: 4,
    isLoto: true,
    banMovil: 0,
    tuJugada: 0,
  },
];
