type Loteria = {
  id: number;
  nombre: string;
  numero_total: number;
  cantidad_premios: number;
  isLoto: boolean;
};

export const loterias: Loteria[] = [
  {
    id: 36,
    nombre: "Anguila 10 AM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 20,
    nombre: "La Primera 12 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 21,
    nombre: "La Suerte 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 33,
    nombre: "King Lottery 12:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 13,
    nombre: "La Real 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 37,
    nombre: "Anguila 1 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },

  {
    id: 31,
    nombre: "Florida Dia 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 12,
    nombre: "Gana Mas 2:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 16,
    nombre: "New York 3:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 15,
    nombre: "Quiniela LoteDom",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 42,
    nombre: "El Quemaito Mayor",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 38,
    nombre: "Anguila 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 29,
    nombre: "La Suerte 6 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },

  {
    id: 34,
    nombre: "King Lottery 7:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 6,
    nombre: "Loteka 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 18,
    nombre: "La Primera 8 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 4,
    nombre: "Loteria Nacional 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 5,
    nombre: "Leidsa 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 40,
    nombre: "Anguila 9 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 32,
    nombre: "Florida 11 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    id: 17,
    nombre: "New York 11:30 PM",
    numero_total: 100,
    cantidad_premios: 3,
    isLoto: false,
  },
  {
    cantidad_premios: 3,
    id: 30,
    isLoto: false,
    nombre: "TODAS LAS QUINIELAS",
    numero_total: 100,
  },
  {
    id: 41,
    nombre: "Loto Real 1PM (M/V)",
    numero_total: 38,
    cantidad_premios: 6,
    isLoto: true,
  },
  {
    id: 10,
    nombre: "Loto 5 8PM (Diario)",
    numero_total: 38,
    cantidad_premios: 5,
    isLoto: true,
  },
  {
    id: 11,
    nombre: "Mega Lotto 8PM (L/J)",
    numero_total: 38,
    cantidad_premios: 6,
    isLoto: true,
  },

  {
    id: 27,
    nombre: "Mega Chance 8PM (Diario)",
    numero_total: 100,
    cantidad_premios: 5,
    isLoto: true,
  },
  {
    id: 23,
    nombre: "Pega 3 Mas 9PM (Diario)",
    numero_total: 50,
    cantidad_premios: 3,
    isLoto: true,
  },

  {
    id: 9,
    nombre: "Loto Leidsa 9PM (M/S)",
    numero_total: 40,
    cantidad_premios: 6,
    isLoto: true,
  },

  {
    id: 7,
    nombre: "Loto Pool 9PM (Diario)",
    numero_total: 31,
    cantidad_premios: 5,
    isLoto: true,
  },

  {
    id: 8,
    nombre: "Super Kino Tv 9PM (Diario)",
    numero_total: 80,
    cantidad_premios: 20,
    isLoto: true,
  },
  {
    id: 39,
    nombre: "Cash4Life",
    numero_total: 60,
    cantidad_premios: 5,
    isLoto: true,
  },
  {
    id: 26,
    nombre: "Mega Millions",
    numero_total: 70,
    cantidad_premios: 5,
    isLoto: true,
  },
  {
    id: 24,
    nombre: "Power Ball",
    numero_total: 70,
    cantidad_premios: 5,
    isLoto: true,
  },
  {
    id: 43,
    nombre: "El Agarra 4",
    numero_total: 100,
    cantidad_premios: 4,
    isLoto: true,
  },
];
